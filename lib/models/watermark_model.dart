import 'package:flutter/material.dart';

// 水印数据模型
class WatermarkModel {
  final String id;
  final String url;
  final String name;
  Offset position;
  double scale;
  double rotation;
  double opacity;
  bool isSelected;

  WatermarkModel({
    required this.id,
    required this.url,
    required this.name,
    this.position = const Offset(100, 100),
    this.scale = 0.3,
    this.rotation = 0.0,
    this.opacity = 0.8,
    this.isSelected = false,
  });

  // 复制方法
  WatermarkModel copyWith({
    String? id,
    String? url,
    String? name,
    Offset? position,
    double? scale,
    double? rotation,
    double? opacity,
    bool? isSelected,
  }) {
    return WatermarkModel(
      id: id ?? this.id,
      url: url ?? this.url,
      name: name ?? this.name,
      position: position ?? this.position,
      scale: scale ?? this.scale,
      rotation: rotation ?? this.rotation,
      opacity: opacity ?? this.opacity,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'url': url,
      'name': name,
      'position_x': position.dx,
      'position_y': position.dy,
      'scale': scale,
      'rotation': rotation,
      'opacity': opacity,
      'isSelected': isSelected,
    };
  }

  // 从Map创建
  factory WatermarkModel.fromMap(Map<String, dynamic> map) {
    return WatermarkModel(
      id: map['id'] ?? '',
      url: map['url'] ?? '',
      name: map['name'] ?? '',
      position: Offset(
        map['position_x']?.toDouble() ?? 100.0,
        map['position_y']?.toDouble() ?? 100.0,
      ),
      scale: map['scale']?.toDouble() ?? 0.3,
      rotation: map['rotation']?.toDouble() ?? 0.0,
      opacity: map['opacity']?.toDouble() ?? 0.8,
      isSelected: map['isSelected'] ?? false,
    );
  }

  @override
  String toString() {
    return 'WatermarkModel(id: $id, url: $url, name: $name, position: $position, scale: $scale, rotation: $rotation, opacity: $opacity, isSelected: $isSelected)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WatermarkModel && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}

// 水印变换状态
class WatermarkTransform {
  final Offset position;
  final double scale;
  final double rotation;

  const WatermarkTransform({
    required this.position,
    required this.scale,
    required this.rotation,
  });

  WatermarkTransform copyWith({
    Offset? position,
    double? scale,
    double? rotation,
  }) {
    return WatermarkTransform(
      position: position ?? this.position,
      scale: scale ?? this.scale,
      rotation: rotation ?? this.rotation,
    );
  }
}
