import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/watermark_model.dart';
import '../providers/watermark_provider.dart';
import '../utils/constants.dart';

// 可拖拽水印组件
class DraggableWatermark extends StatefulWidget {
  final WatermarkModel watermark;
  final Size containerSize;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  const DraggableWatermark({
    super.key,
    required this.watermark,
    required this.containerSize,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  @override
  State<DraggableWatermark> createState() => _DraggableWatermarkState();
}

class _DraggableWatermarkState extends State<DraggableWatermark> {
  late Offset _position;
  late double _scale;
  late double _rotation;
  double _baseRotation = 0.0; // 记录旋转开始时的基础角度
  double _baseScale = 1.0; // 记录缩放开始时的基础缩放值

  @override
  void initState() {
    super.initState();
    _position = widget.watermark.position;
    _scale = widget.watermark.scale;
    _rotation = _normalizeRotation(widget.watermark.rotation);
  }

  // 标准化旋转值到-π到π之间
  double _normalizeRotation(double rotation) {
    double normalized = rotation % (2 * 3.14159);
    if (normalized > 3.14159) normalized -= 2 * 3.14159;
    if (normalized < -3.14159) normalized += 2 * 3.14159;
    return normalized;
  }

  @override
  void didUpdateWidget(DraggableWatermark oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.watermark != widget.watermark) {
      _position = widget.watermark.position;
      _scale = widget.watermark.scale;
      _rotation = _normalizeRotation(widget.watermark.rotation);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onTap: () {
          widget.onTap?.call();
          // 选中当前水印
          context.read<WatermarkProvider>().selectActiveWatermark(
            widget.watermark.id,
          );
        },
        onDoubleTap: widget.onDoubleTap,
        onLongPress: () {
          // 长按显示删除确认对话框
          _showDeleteConfirmDialog(context);
        },
        onScaleStart: (details) {
          // 选中当前水印
          context.read<WatermarkProvider>().selectActiveWatermark(
            widget.watermark.id,
          );
          // 记录开始时的旋转角度和缩放值
          _baseRotation = _rotation;
          _baseScale = _scale;
        },
        onScaleUpdate: (details) {
          setState(() {
            // 简化操作逻辑：分离移动、缩放和旋转
            if (details.pointerCount == 1) {
              // 单指拖拽：只处理移动
              _position += details.focalPointDelta;

              // 限制在容器范围内，使用更宽松的边界检测
              // 允许水印部分超出边界，但保持中心点在容器内
              _position = Offset(
                _position.dx.clamp(
                  -50.0, // 允许左侧超出50像素
                  widget.containerSize.width + 50.0, // 允许右侧超出50像素
                ),
                _position.dy.clamp(
                  -50.0, // 允许顶部超出50像素
                  widget.containerSize.height + 50.0, // 允许底部超出50像素
                ),
              );
            } else if (details.pointerCount == 2) {
              // 双指操作：处理缩放和旋转

              // 缩放处理（基于初始缩放值）
              final newScale = (_baseScale * details.scale).clamp(
                AppConstants.minWatermarkScale,
                AppConstants.maxWatermarkScale,
              );
              _scale = newScale;

              // 旋转处理（基于初始角度，降低敏感度）
              _rotation = _normalizeRotation(
                _baseRotation + details.rotation * 0.5,
              );
            }
          });

          // 更新Provider中的变换
          context.read<WatermarkProvider>().updateWatermarkPosition(
            widget.watermark.id,
            _position,
          );
          context.read<WatermarkProvider>().updateWatermarkScale(
            widget.watermark.id,
            _scale,
          );
          context.read<WatermarkProvider>().updateWatermarkRotation(
            widget.watermark.id,
            _rotation,
          );
        },
        onScaleEnd: (details) {
          // 缩放结束
        },
        child: Transform.scale(
          scale: _scale,
          child: Transform.rotate(
            angle: _rotation,
            child: Container(
              decoration: const BoxDecoration(
                // 移除边框和圆角
              ),
              child: Stack(
                children: [
                  // 水印图像（设为不透明）
                  Opacity(
                    opacity: 1.0, // 强制设为不透明
                    child: CachedNetworkImage(
                      imageUrl: widget.watermark.url,
                      fit: BoxFit.contain,
                      imageBuilder: (context, imageProvider) {
                        return FutureBuilder<Size>(
                          future: _getImageSize(imageProvider),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              final imageSize = snapshot.data!;
                              final displaySize = _calculateDisplaySize(
                                imageSize,
                              );

                              return Image(
                                image: imageProvider,
                                width: displaySize.width,
                                height: displaySize.height,
                                fit: BoxFit.contain,
                              );
                            }
                            return Container(
                              width: AppConstants.defaultWatermarkSize,
                              height: AppConstants.defaultWatermarkSize,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              ),
                            );
                          },
                        );
                      },
                      placeholder: (context, url) => Container(
                        width: AppConstants.defaultWatermarkSize,
                        height: AppConstants.defaultWatermarkSize,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Center(
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: AppConstants.defaultWatermarkSize,
                        height: AppConstants.defaultWatermarkSize,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(Icons.error, color: Colors.red),
                      ),
                    ),
                  ),

                  // 移除所有操作按钮，使用长按删除
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 获取图片的原始尺寸
  Future<Size> _getImageSize(ImageProvider imageProvider) async {
    final completer = Completer<Size>();
    final imageStream = imageProvider.resolve(const ImageConfiguration());

    late ImageStreamListener listener;
    listener = ImageStreamListener((ImageInfo info, bool synchronousCall) {
      final image = info.image;
      completer.complete(Size(image.width.toDouble(), image.height.toDouble()));
      imageStream.removeListener(listener);
    });

    imageStream.addListener(listener);
    return completer.future;
  }

  // 计算显示尺寸，按照新的逻辑实现智能缩放
  Size _calculateDisplaySize(Size imageSize) {
    final containerWidth = widget.containerSize.width;
    final containerHeight = widget.containerSize.height;

    // 默认按照水印图片的原始大小显示
    double displayWidth = imageSize.width;
    double displayHeight = imageSize.height;

    // 如果水印宽度大于app宽度，则调整为app宽度，高度等比缩放
    if (displayWidth > containerWidth) {
      final widthScale = containerWidth / displayWidth;
      displayWidth = containerWidth;
      displayHeight = displayHeight * widthScale;
    }

    // 如果调整后的高度超出显示区域，则以高度为准，宽度等比缩放
    if (displayHeight > containerHeight) {
      final heightScale = containerHeight / displayHeight;
      displayHeight = containerHeight;
      displayWidth = displayWidth * heightScale;
    }

    return Size(displayWidth, displayHeight);
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('删除水印'),
          content: const Text('确定要删除这个水印吗？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
                // 删除水印
                context.read<WatermarkProvider>().removeWatermark(
                  widget.watermark.id,
                );
                widget.onLongPress?.call();
              },
              child: const Text('删除', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
